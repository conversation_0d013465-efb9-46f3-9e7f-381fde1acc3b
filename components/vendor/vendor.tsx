import { Upload } from 'lucide-react';
import moment from 'moment';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import VendorBulkUploadModal from '@/components/vendor/bulkUploadModal';
import CreateVendorModal from '@/components/vendor/modal/createVendorModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TVendorData } from '@/interfaces/vendor';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { ValueFormatterParams } from '@ag-grid-community/core';

const VendorHub = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);
  const router = useRouter();
  const { data, isLoading, reFetch } = useFetch(accessToken, 'vendors');

  const [createVendor, setCreateVendor] = useState(false);
  const [editVendor, setEditVendor] = useState(false);
  const [bulkUpload, setBulkUpload] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<undefined | TVendorData>(
    undefined,
  );
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const {
    deleteData,
    response: onDelete,
    isLoading: deleteLoading,
  } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete, reFetch]);

  const handleEdit = (rowData: TVendorData) => {
    setSelectedVendor(rowData);
    setEditVendor(true);
  };

  const handleDeleteModal = (rowData: TVendorData) => {
    setSelectedVendor(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      await deleteData(accessToken, `vendors/${selectedVendor?.id}`);
    }
    fetch();
  };

  // Define column type
  type VendorColumnDef = {
    headerName: string;
    field: string;
    sortable?: boolean;
    resizable?: boolean;
    getQuickFilterText?: (params: { value: unknown }) => string;
    valueFormatter?: (params: ValueFormatterParams) => string;
    filter?: boolean | string;
    cellRenderer?: (params: { data: unknown }) => JSX.Element;
    width?: number;
    pinned?: string;
  };

  const getVendorColumns = useCallback(() => {
    const vendorColumns: VendorColumnDef[] = [
      {
        headerName: 'Vendor Name',
        field: 'name',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params) => {
          return String(params.value || '');
        },
        valueFormatter: (params) => getValueOrDefault(params.data, 'name'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Product/Service',
        field: 'service',
        resizable: true,
        sortable: true,
        valueFormatter: (params) => getValueOrDefault(params.data, 'service'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Assignee',
        field: 'full_name',
        resizable: true,
        sortable: true,
        valueFormatter: (params) =>
          getValueOrDefault(params.data.assignee_details, 'full_name'),
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Status',
        field: 'status',
        resizable: true,
        sortable: true,
        filter: 'agMultiColumnFilter',
        valueFormatter: (params) => getValueOrDefault(params.data, 'status'),
      },
      {
        headerName: 'Last evaluation date',
        field: 'last_evaluation_date',
        resizable: true,
        sortable: true,
        valueFormatter: (params) =>
          params.data.last_evaluation_date
            ? moment(params.data.last_evaluation_date).format('DD-MMM-YYYY')
            : '--',
        filter: 'agMultiColumnFilter',
      },
      {
        headerName: 'Next evaluation date',
        field: 'next_evaluation_date',
        resizable: true,
        sortable: true,
        valueFormatter: (params) =>
          params.data.next_evaluation_date
            ? moment(params.data.next_evaluation_date).format('DD-MMM-YYYY')
            : '--',
        filter: 'agMultiColumnFilter',
      },
    ];

    if (hasAccess(AccessActions.VendorAdmin, user)) {
      vendorColumns.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={!hasAccess(AccessActions.VendorAdmin, user)}
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });
    }
    return vendorColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'Vendor hub',
      link: '/vendor',
    },
  ];

  return (
    <Layout>
      {editVendor && selectedVendor && (
        <Dialog open={editVendor} onOpenChange={setEditVendor}>
          <CreateVendorModal
            edit
            vendorData={selectedVendor}
            setOpenEdit={setEditVendor}
            reFetch={reFetch}
          />
        </Dialog>
      )}
      {createVendor && (
        <Dialog open={createVendor} onOpenChange={setCreateVendor}>
          <CreateVendorModal setOpenEdit={setCreateVendor} reFetch={reFetch} />
        </Dialog>
      )}
      {bulkUpload && (
        <Dialog open={bulkUpload} onOpenChange={setBulkUpload}>
          <VendorBulkUploadModal
            setOpenModal={setBulkUpload}
            onSuccess={reFetch}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Vendor`}
            infoText={`Are you sure you want to delete this vendor?`}
            btnText={'Delete'}
            onClick={handleDelete}
            btnLoading={deleteLoading}
          />
        </Dialog>
      )}
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Vendor Hub
          </div>
        </div>
      </div>
      <div className="mb-5 relative">
        {hasAccess(AccessActions.VendorAdmin, user) && (
          <div className="flex gap-4 absolute top-0 right-0 z-10">
            <Tooltip>
              <TooltipTrigger>
                <SecondaryButton
                  text="Bulk Upload"
                  size="medium"
                  buttonClasses="!px-4 !py-2"
                  onClick={() => setBulkUpload(true)}
                  icon={<Upload className="w-4 h-4" />}
                />
              </TooltipTrigger>
              <TooltipContent>
                Bulk Upload Vendors from Excel (Admin Only)
              </TooltipContent>
            </Tooltip>
            <PrimaryButton
              icon={<PlusIcon color="white" />}
              text="Add Vendor"
              buttonClasses="!px-5 !py-2"
              onClick={() => setCreateVendor(true)}
            />
          </div>
        )}

        <CommonTable
          data={data}
          columnDefs={getVendorColumns() as any}
          handleRowClick={(e) => {
            router.push({
              pathname: `/vendor/${e.data.id}`,
              query: router.query,
            });
          }}
          searchPlaceholder={
            'Search by name, service, contact details or status'
          }
          isLoading={isLoading}
        />
      </div>
    </Layout>
  );
};

export default VendorHub;
