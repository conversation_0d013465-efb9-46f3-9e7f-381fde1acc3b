import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';

interface IProps {
  setOpenRequirement: React.Dispatch<React.SetStateAction<boolean>>;
  subClauseId: string;
  selectedStatus: string;
  reFetch: () => void;
}

type RequestBody = {
  status: string;
  sub_clause_id: string;
  comment?: string;
  category?: string;
  description?: string;
};

const RaiseClauseStatusModal: React.FC<IProps> = ({
  setOpenRequirement,
  subClauseId,
  selectedStatus,
  reFetch,
}) => {
  const [requirement, setRequirement] = useState('');

  const params = useParams();
  const { accessToken } = useAuthStore();

  const {
    postData: postRequirement,
    response: postRequirementResponse,
    isLoading: postRequirementLoading,
  } = usePost();

  const isCompliant = selectedStatus === 'Compliant';
  const isLoading = postRequirementLoading;
  const isSubmitDisabled = !requirement.trim();

  const buildRequestBody = useCallback((): RequestBody => {
    const baseBody = {
      sub_clause_id: subClauseId,
      status: isCompliant ? 'Compliant' : 'Open',
    };

    if (isCompliant) {
      return {
        ...baseBody,
        comment: requirement.trim(),
      };
    }

    return {
      ...baseBody,
      category: selectedStatus,
      description: requirement.trim(),
    };
  }, [subClauseId, selectedStatus, requirement, isCompliant]);

  const getApiEndpoint = useCallback((): string => {
    const baseUrl = `audits/${params?.auditId}`;
    return isCompliant ? `${baseUrl}/subclause` : `${baseUrl}/non_conformities`;
  }, [params?.auditId, isCompliant]);

  const handleSubmit = useCallback(async () => {
    if (!accessToken || !params?.auditId || !requirement.trim()) return;

    const body = buildRequestBody();
    const endpoint = getApiEndpoint();

    console.log('API call with body:', body);

    await postRequirement(accessToken, endpoint, body);
  }, [
    accessToken,
    params?.auditId,
    requirement,
    subClauseId,
    selectedStatus,
    buildRequestBody,
    getApiEndpoint,
    postRequirement,
  ]);

  const handleRequirementChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setRequirement(e.target.value);
    },
    [],
  );

  const closeModal = useCallback(() => {
    setRequirement('');
    setOpenRequirement(false);
  }, [setOpenRequirement]);

  useEffect(() => {
    if (postRequirementResponse) {
      reFetch();
      closeModal();
    }
  }, [postRequirementResponse]);

  const modalTitle = isCompliant ? 'Compliant' : `Raise a ${selectedStatus}`;
  const placeholderText = 'Provide Justification';

  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>{modalTitle}</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Justification<span className="text-[#F55D5D]">*</span>
        </div>
        <textarea
          rows={4}
          name="description"
          value={requirement}
          placeholder={placeholderText}
          onChange={handleRequirementChange}
          className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
          disabled={isLoading}
        />
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={isLoading}
            onClick={handleSubmit}
            disabled={isSubmitDisabled}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default RaiseClauseStatusModal;
