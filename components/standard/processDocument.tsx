import { <PERSON>, <PERSON>us, <PERSON><PERSON>, EyeIcon } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import CheckIcon from '@/assets/outline/check';
import EditIcon from '@/assets/outline/edit';
import UnlinkIcon from '@/assets/outline/unlink';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import { IProcess } from '@/interfaces/process';
import { IDocument, Process } from '@/interfaces/standard';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';
import { getValueOrDefault } from '@/utils/table';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../common/accordion';
import { Dialog, DialogTrigger } from '../common/dialog';
import DeleteModal from '../common/modals/deleteModal';
import CommonTable from '../common/table';
import OtpModal from '../document/components/modals/otpModal';
import LinkProcessModal from './standardPage/linkProcessModal';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import axios from 'axios';
import DocumentViewModalWithSidebar from '../document/components/modals/documentViewModalWithSidebar';
import { useParams } from 'next/navigation';

const NEXT_PUBLIC_URL = process.env.NEXT_PUBLIC_URL;
const NEXT_PUBLIC_VERSION = process.env.NEXT_PUBLIC_VERSION;

interface Response {
  id: string;
  step: string;
}

interface IProps {
  canEdit?: boolean;
  process: {
    id: string;
    name: string;
    documents: IDocument[];
  };
  subClause: {
    id: string;
    clause_no: string;
    title: string;
    description: string;
    question: string;
    is_compliant: boolean;
    processes: Process[];
  };
  reFetch: () => void;
  index: number;
  processes: {
    records: IProcess[];
  } | null;
  isLoading?: boolean;
}

const ProcessDocument = ({
  process,
  subClause,
  reFetch,
  index,
  processes,
  canEdit = true,
  isLoading,
}: IProps) => {
  const [editProcessModal, setEditProcessModal] = useState(false);
  const [selectedProcess, setSelectedProcess] = useState<string | null>(null);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);

  const [documentData, setDocumentData] = useState<any>(null);

  const { accessToken, user } = useAuthStore();
  const cfr_enabled = useAuthStore(
    (state) => state.user?.company.is_cfr11_required,
  );
  const router = useRouter();
  const {
    deleteData,
    isLoading: deleteLoading,
    response,
    error,
  } = useDelete<Response>();
  const [otpModal, setOtpModal] = useState(false);
  const [mfaSessionId, setMfaSessionId] = useState('');
  const [documentViewModal, setDocumentViewModal] = useState(false);
  const param = useParams();

  // Get standardId or auditId from params, with fallback to query params
  const urlId =
    param?.standardId ||
    param?.auditId ||
    (typeof router.query?.standardId === 'string'
      ? router.query.standardId
      : typeof router.query?.auditId === 'string'
      ? router.query.auditId
      : '');

  const orgId =
    typeof window !== 'undefined'
      ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
      : null;

  const handleGetDocument = async (documentId: string) => {
    if (documentId) {
      const baseUrl = NEXT_PUBLIC_URL;
      const productVersion = NEXT_PUBLIC_VERSION;

      const config = {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      // Extract source from URL path
      const sourcePath =
        router.asPath.split('?')[0].split('/')[1] || 'standard';

      // Build URL with proper fallbacks to prevent 'undefined' in URL
      const url = `${baseUrl}/${productVersion}/documents/${documentId}?source=${sourcePath}&id=${
        urlId || ''
      }`;

      console.log('Document API URL:', url);
      try {
        const response = await axios.get(url, config);
        console.log(response.data.record);
        setDocumentData(response.data.record);
        setDocumentViewModal(true);
        return;
      } catch (error) {
        console.error('Error fetching clauses:', error);
      }
    }
  };

  const unlinkProcess = async (subClauseId: string, documentID: string) => {
    await deleteData(
      accessToken as string,
      `clauses/${subClauseId}/unlink-document/`,
      {
        process_id: process.id,
        document_ids: [documentID],
      },
    );
  };

  const assetColumns: any = [
    {
      headerName: 'Document Id',
      field: 'doc_id',
      sortable: true,
      resizable: true,
      filter: true,

      valueFormatter: (params: any) =>
        getValueOrDefault(params?.data, 'doc_id'),
    },
    {
      headerName: 'Title',
      field: 'title',
      resizable: true,
      filter: true,
      valueFormatter: (params: any) => getValueOrDefault(params?.data, 'title'),

      minWidth: 350,
      flex: 2,
    },
    {
      headerName: 'Assignees',
      field: 'assignees.name',
      sortable: false,
      resizable: true,
      filter: 'agMultiColumnFilter',
      filterValueGetter: (params: any) => {
        return params?.data?.assignees
          ? params?.data?.assignees
              ?.map((process: { name: string }) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
      getQuickFilterText: (params: any) => {
        return params?.data?.assignees
          ?.map((process: any) => {
            return `${process.name}`;
          })
          .join(', ');
      },
      valueFormatter: (params: any) => {
        return params?.data?.assignees
          ? params?.data?.assignees
              ?.map((process: any) => {
                return `${process.name}`;
              })
              .join(', ')
          : '-';
      },
    },
    {
      headerName: 'Status',
      field: 'status',
      resizable: true,
      filter: false,
    },
    {
      headerName: 'Compliant',
      field: 'complaint',
      sortable: true,
      resizable: true,
      filter: false,
      cellRenderer: (params: any) => (
        <div
          className={cn(
            'text-primary-500 cursor-pointer h-5 w-5 flex items-center justify-center  rounded-full absolute top-[50%] translate-y-[-50%]',
            params.data.is_compliant ? 'bg-green-200' : 'bg-red-200',
          )}
        >
          {params.data.is_compliant ? (
            <Check className="h-3 w-3" color="#fff" />
          ) : (
            <Minus className="h-3 w-3" color="#fff" />
          )}
        </div>
      ),
    },
    {
      headerName: 'Action',
      field: 'action',
      resizable: true,
      filter: false,
      cellRenderer: (params: any) => (
        <div className="flex items-center space-x-2 mt-1">
          <Dialog open={otpModal} onOpenChange={setOtpModal}>
            {otpModal && (
              <OtpModal
                setOtpModal={setOtpModal}
                sessionId={mfaSessionId}
                refetchDocumentData={reFetch}
              />
            )}
          </Dialog>

          <Tooltip>
            <TooltipTrigger>
              {(params.data.status === 'Published' ||
                params.data.status === 'Past Due') && (
                <div
                  className="text-black bg-white-150 rounded-full cursor-pointer h-10 w-10 flex items-center justify-center"
                  onClick={() => {
                    handleGetDocument(
                      (params.data.status === 'Published' ||
                        params.data.status === 'Past Due') &&
                        params.data.id,
                    );
                  }}
                >
                  <EyeIcon className="h-6 w-6" />
                </div>
              )}
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm text-dark-300">View Document</div>
            </TooltipContent>
          </Tooltip>

          {canEdit && (
            <Dialog>
              <DialogTrigger>
                {hasAccess(AccessActions.CanLinkDocument, user) ? (
                  <div
                    className="text-primary-500 cursor-pointer h-10 w-10 flex items-center justify-center bg-white-200 rounded-full hover:bg-white-300"
                    onClick={() => console.log(params, process)}
                  >
                    <UnlinkIcon className="h-6 w-6" />
                  </div>
                ) : (
                  '-'
                )}
              </DialogTrigger>
              <DeleteModal
                title={'Unlink document'}
                infoText={'Are you sure you want to unlink this document? '}
                btnText={'Unlink'}
                onClick={() => unlinkProcess(subClause.id, params.data.id)}
                btnLoading={deleteLoading}
              />
            </Dialog>
          )}
        </div>
      ),
    },
  ];


  useEffect(() => {
    if (response) {
      if (cfr_enabled && response.step === 'mfa') {
        setMfaSessionId(response.id);
        setOtpModal(true);
      } else {
        reFetch();
      }
    }

    if (error) {
      reFetch();
    }
  }, [response, error]);

  const isCompliant = process.documents.some(
    (document) => document.is_compliant,
  );

  if (isLoading) {
    return (
      <div className="w-full mt-3 border rounded-lg p-3 animate-pulse space-y-4">
        {/* Accordion header skeleton */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-5 w-5 rounded-full bg-gray-200"></div>
            <div className="h-5 w-40 rounded bg-gray-200"></div>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-6 w-6 rounded-full bg-gray-200"></div>
            <div className="h-6 w-6 rounded-full bg-gray-200"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Accordion type="single" collapsible className="w-full mt-3">
        <AccordionItem value="item-1" className="">
          <AccordionTrigger
            className="group"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <div className="flex flex-1 items-center justify-between mr-2">
              <div className="flex items-center gap-2">
                <span>{index + 1}</span>
                <div className="text-dark-300 text-base leading-7 font-medium">
                  {process.name}
                </div>
              </div>
              <div className="flex items-center gap-2">
                {canEdit && (
                  <Dialog
                    open={editProcessModal}
                    onOpenChange={setEditProcessModal}
                  >
                    <DialogTrigger
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedProcess(process.id);
                        setSelectedDocuments(
                          process.documents.map((e) => e.id),
                        );
                      }}
                    >
                      {hasAccess(AccessActions.CanLinkDocument, user) && (
                        <button className="h-7 w-7 rounded-full bg-white-200 hidden items-center justify-center group-hover:flex cursor-pointer hover:bg-white-300">
                          <EditIcon height="20" width="20" />
                        </button>
                      )}
                    </DialogTrigger>
                    <LinkProcessModal
                      subclauseId={subClause.id}
                      edit
                      selectedProcessId={selectedProcess}
                      selectedDocumentId={selectedDocuments}
                      setShowModal={setEditProcessModal}
                      showModal={editProcessModal}
                      refetch={reFetch}
                      processes={processes}
                    />
                  </Dialog>
                )}

                <div className="h-7 w-7 flex justify-center items-center">
                  <div
                    className={cn(
                      'h-5 w-5 flex items-center justify-center  rounded-full',
                      isCompliant ? 'bg-green-200' : 'bg-red-200',
                    )}
                  >
                    {isCompliant ? (
                      <CheckIcon height="16" width="16" />
                    ) : (
                      <Minus height="16" width="16" color="#fff" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="py-4 px-5 flex flex-col gap-4">
            <div>
              <CommonTable
                data={{
                  records: process.documents,
                }}
                columnDefs={assetColumns}
                searchBox={false}
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Dialog open={documentViewModal} onOpenChange={setDocumentViewModal}>
        {documentViewModal && (
          <DocumentViewModalWithSidebar
            setDocumentViewModal={setDocumentViewModal}
            extension={documentData?.document_version?.file_extension}
            filePath={documentData?.document_version?.file_path}
            title={documentData?.title}
            version={documentData?.document_version?.version_number}
            publishDate={documentData?.publish_date}
            status={documentData?.status}
            next_review_date={documentData?.next_review_date}
            review_period={documentData?.review_period}
            source={router.asPath.split('?')[0].split('/')[1]}
          />
        )}
      </Dialog>

    </>
  );
};

export default ProcessDocument;
