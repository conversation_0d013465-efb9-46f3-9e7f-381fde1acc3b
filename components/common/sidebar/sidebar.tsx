import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import BPRGearLogo from '@/assets/gearLogo';
import BPRLogo from '@/assets/logo';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';
import {
  Box,
  ChevronDown,
  ChevronLeftIcon,
  ChevronRight,
  ChevronUp,
  FileCog,
  LucideArchive,
  LucideBoxes,
  LucideCircleArrowUp,
  LucideFileText,
  LucideFlag,
  LucideHandshake,
  LucideSearchCheck,
  LucideShield,
  LucideUsers,
  LucideWrench,
  ShieldCheck,
  User2,
} from 'lucide-react';
import { useRouter } from 'next/router';
import { Popover, PopoverContent, PopoverTrigger } from '../popover';

import LogoutIcon from '@/assets/outline/logout';
import SettingIcon from '@/assets/outline/settting';
import ProductionHubIcon from '@/assets/sidebarIcon/productionHub';
import { ComplianceChat } from '@/components/compliance-chat/ComplianceChat';
import { AccessActions } from '@/constants/access';
import { ORGANIZATION_SESSION_KEY } from '@/constants/common';

import MenuPopoverContent from '../menuPopoverContent';

const aiEnabledCompanies = [
  '6bc3bab7-bbb3-4697-b586-ce8d0d52158d', //Plumage
  '6d88182b-0581-4667-93a5-3c71a8c6f49f', //Plumage
  'af04b4ab-8174-40a9-9589-7250437e7ea5', //prod BPRHub
  '780a27d6-3e62-4ac7-886f-f94ff38cb1c5', //dev BPRHub
  'af04b4ab-8174-40a9-9589-7250437e7ea5', //qa BPRHub
];

const Sidebar = () => {
  const {
    user,
    setIsLoading,
    complianceExpanded,
    operationsExpanded,
    setComplianceExpanded,
    setOperationsExpanded,
    sideBarExpanded,
    setSideBarExpanded,
  } = useAuthStore();
  const router = useRouter();

  const [selectedTab, setSelectedTab] = useState('');
  const pathname = usePathname();

  const [operationsOpen, setOperationsOpen] = useState(false);
  const [complianceOpen, setComplianceOpen] = useState(false);
  const [userProfile, setUserProfile] = useState(false);

  const handleLogout = async () => {
    setIsLoading(true);
    window.location.href = '/api/auth/logout';
    sessionStorage.removeItem(ORGANIZATION_SESSION_KEY);
    router.push('/login');
  };

  const menuPopoverData = [
    {
      id: '1',
      label: 'Settings',
      icon: <SettingIcon height="20" width="20" color="#282828" />,
      selected: false,
      onClick: () => {
        router.push('/setting');
      },
      access: hasAccess(AccessActions.CanCreateUser, user),
    },
    {
      id: '2',
      label: 'Logout',
      icon: <LogoutIcon height="20" width="20" color="#282828" />,
      selected: false,
      onClick: () => {
        handleLogout();
      },
      access: true,
    },
  ];

  const ComplianceHubData = [
    {
      id: 'standard',
      label: 'Standards',
      icon: (
        <LucideShield
          className={
            selectedTab.includes('/standard')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/standard',
      access: true,
    },
    {
      id: 'document',
      label: 'Documents',
      icon: (
        <LucideFileText
          className={
            selectedTab.includes('/document')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/document',
      access: hasAccess(AccessActions.ShowSidebarDocument, user),
    },
    {
      id: 'risk',
      label: 'Risks',
      icon: (
        <LucideFlag
          className={
            selectedTab.includes('/risk')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/risk',
      access: hasAccess(AccessActions.VIEW_RISK, user),
    },
    {
      id: 'improvement',
      label: 'Improvements',
      icon: (
        <LucideCircleArrowUp
          className={
            selectedTab.includes('/improvement')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/improvement',
      access: hasAccess(AccessActions.ShowSidebarImprovement, user),
    },
    {
      id: 'audit',
      label: 'Audits',
      icon: (
        <LucideSearchCheck
          className={
            selectedTab.includes('/audit')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/audit',
      access: hasAccess(AccessActions.ShowSidebarAudit, user),
    },
  ];

  const OperationsHubData = [
    {
      id: 'inventory',
      label: 'Inventory',
      icon: (
        <LucideArchive
          className={
            selectedTab.includes('/inventory')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/inventory',
      access: hasAccess(AccessActions.ShowSidebarInventory, user),
    },
    {
      id: 'asset',
      label: 'Assets',
      icon: (
        <LucideWrench
          className={
            selectedTab.includes('/asset')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/asset',
      access: hasAccess(AccessActions.ShowSidebarAsset, user),
    },
    {
      id: 'vendor',
      label: 'Vendors',
      icon: (
        <LucideHandshake
          className={
            selectedTab.includes('/vendor')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/vendor',
      access: hasAccess(AccessActions.ShowSidebarVendor, user),
    },
    {
      id: 'people',
      label: 'People',
      icon: (
        <LucideUsers
          className={
            selectedTab.includes('/people')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/people',
      access: hasAccess(AccessActions.ShowSidebarPeople, user),
    },
    {
      id: 'master',
      label: 'Products',
      icon: (
        <LucideBoxes
          className={
            selectedTab.includes('/product-hub')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/product-hub',
      access: hasAccess(AccessActions.ShowSidebarMaster, user),
    },
    {
      id: 'production',
      label: 'Production',
      icon: (
        <Box
          className={
            selectedTab.includes('/production')
              ? 'text-primary-500'
              : 'text-white-150 group-hover:text-dark-300 transition-colors'
          }
        />
      ),
      link: '/production',
      access: hasAccess(AccessActions.ShowSidebarProduction, user),
    },
  ];

  useEffect(() => {
    if (pathname) {
      setSelectedTab(pathname);
    }
  }, [pathname]);

  useEffect(() => {
    if (pathname === '/') {
      setSelectedTab('/standard');
    }
  }, []);

  // Filter accessible items for each hub
  const accessibleComplianceItems = ComplianceHubData.filter(
    (item) => item.access,
  );
  const accessibleOperationsItems = OperationsHubData.filter(
    (item) => item.access,
  );

  return (
    <div
      className={cn(
        'overflow-visible relative z-50 w-[16.25rem] h-screen flex flex-col border-r border-grey-100 bg-[#1e293b] px-0 pb-4 transition-all pt-[1.64rem]',
        !sideBarExpanded ? '!w-[6.2rem] !px-2 !py-8' : '',
      )}
    >
      <div
        className={cn(
          'absolute z-[60] h-7 w-7 bg-[#1e293b] shadow-lg border border-slate-500 rounded-full hover:bg-[#1e293b] flex items-center justify-center cursor-pointer',
          sideBarExpanded ? '-right-3 top-7' : '-right-3 top-10',
          !sideBarExpanded && 'rotate-180',
        )}
        onClick={() => setSideBarExpanded(!sideBarExpanded)}
      >
        <ChevronLeftIcon color="#F6F6F6" />
      </div>
      <div
        className={cn(
          'flex items-center justify-between pb-5 border-b mx-5 border-grey-100 mb-5',
          !sideBarExpanded ? 'flex-col border-b-0 mb-5 pb-0' : '',
        )}
      >
        <div
          className={cn(
            !sideBarExpanded ? 'pb-5 mb-5 border-b border-grey-100 ' : 'flex',
          )}
        >
          {sideBarExpanded ? (
            <BPRLogo color={'#F6F6F6'} width={'140'} height="30" />
          ) : (
            <BPRGearLogo color={'#F6F6F6'} width="40" height="40" />
          )}
        </div>
      </div>
      <div
        className={cn(
          'flex-1 overflow-x-hidden overflow-y-auto flex flex-col',
          !sideBarExpanded ? 'gap-3' : 'px-5',
        )}
      >
        {/* Compliance Hub Group */}
        {accessibleComplianceItems.length > 0 && (
          <div className="mb-2">
            {sideBarExpanded ? (
              <div
                className="flex items-center justify-between px-3 py-2 text-xs font-medium uppercase tracking-wide cursor-pointer group hover:bg-gray-50 rounded-lg transition-colors"
                onClick={() => setComplianceExpanded(!complianceExpanded)}
              >
                <span className="text-base font-bold text-white-150 group-hover:text-dark-300 transition-colors">
                  Compliance Hub
                </span>
                {complianceExpanded ? (
                  <ChevronDown className="h-5 w-5 text-white-150 group-hover:text-dark-300 transition-colors" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-white-150 group-hover:text-dark-300 transition-colors" />
                )}
              </div>
            ) : (
              <Popover open={operationsOpen} onOpenChange={setOperationsOpen}>
                <div
                  onMouseEnter={() => setOperationsOpen(true)}
                  onMouseLeave={() => setOperationsOpen(false)}
                  className="relative"
                >
                  <PopoverTrigger asChild>
                    <div className="hover:bg-white-200 hover:text-dark-300 text-white-150 h-14 w-14 rounded-lg flex items-center justify-center text-sm font-bold transition-all duration-200 cursor-pointer mx-auto">
                      <ShieldCheck
                        width={36}
                        height={36}
                        className="stroke-current"
                      />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent
                    side="right"
                    align="start"
                    className="w-56 px-4 py-2 bg-[#1e293b] shadow-lg rounded-lg"
                    sideOffset={24}
                  >
                    <div className="mb-3 px-2 py-1 text-base font-semibold text-white-150">
                      COMPLIANCE HUB
                    </div>
                    <div className="space-y-1">
                      {accessibleComplianceItems.map((item) => (
                        <Link
                          key={item.id}
                          href={item.link}
                          className={cn(
                            'flex items-center gap-3 px-3 py-2 rounded-md font-medium transition-all group',
                            selectedTab.includes(item.link)
                              ? 'bg-primary-100 text-dark-300'
                              : 'text-white-150 hover:bg-white-200 hover:text-dark-300',
                          )}
                        >
                          <div
                            className={cn(
                              'transition-colors',
                              selectedTab.includes(item.link)
                                ? 'text-primary-500'
                                : 'text-white-150 group-hover:text-dark-300',
                            )}
                          >
                            {item.icon}
                          </div>
                          {item.label}
                        </Link>
                      ))}
                    </div>
                  </PopoverContent>
                </div>
              </Popover>
            )}
            <div
              className={cn(
                'flex flex-col transition-all duration-200 ease-in-out overflow-hidden mt-2',
                !sideBarExpanded ? 'gap-1' : 'gap-2',
                sideBarExpanded && !complianceExpanded
                  ? 'max-h-0'
                  : 'max-h-none',
              )}
            >
              {sideBarExpanded &&
                accessibleComplianceItems.map((item) => (
                  <MenuLinks
                    key={item.id}
                    {...item}
                    active={selectedTab.includes(item.link)}
                    icon={item.icon}
                    sideBarExpanded={sideBarExpanded}
                    access={item.access || false}
                  />
                ))}
            </div>
          </div>
        )}

        {/* Operations Hub Group */}
        {accessibleOperationsItems.length > 0 && (
          <div className="mb-2">
            {sideBarExpanded ? (
              <div
                className="flex items-center justify-between px-3 py-2 text-xs font-medium uppercase tracking-wide cursor-pointer group hover:bg-gray-50 rounded-lg transition-colors"
                onClick={() => setOperationsExpanded(!operationsExpanded)}
              >
                <span className="text-base font-bold text-white-150 group-hover:text-dark-300 transition-colors">
                  Operations Hub
                </span>
                {operationsExpanded ? (
                  <ChevronDown className="h-5 w-5 text-white-150 group-hover:text-dark-300 transition-colors" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-white-150 group-hover:text-dark-300 transition-colors" />
                )}
              </div>
            ) : (
              <Popover open={complianceOpen} onOpenChange={setComplianceOpen}>
                <div
                  onMouseEnter={() => setComplianceOpen(true)}
                  onMouseLeave={() => setComplianceOpen(false)}
                  className="relative"
                >
                  <PopoverTrigger asChild>
                    <div className="hover:bg-white-200 hover:text-dark-300 text-white-150 h-14 w-14 rounded-lg flex items-center justify-center text-sm font-bold transition-all duration-200 cursor-pointer mx-auto">
                      <FileCog
                        width={36}
                        height={36}
                        className="stroke-current"
                      />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent
                    side="right"
                    align="start"
                    className="w-56 px-4 py-2 bg-[#1e293b] shadow-lg rounded-lg"
                    sideOffset={24}
                  >
                    <div className="mb-3 px-2 py-1 text-base font-semibold text-white-150">
                      OPERATIONS HUB
                    </div>
                    <div className="space-y-1">
                      {accessibleOperationsItems.map((item) => (
                        <Link
                          key={item.id}
                          href={item.link}
                          className={cn(
                            'flex items-center gap-3 px-3 py-2 rounded-md font-medium transition-all group',
                            selectedTab.includes(item.link)
                              ? 'bg-primary-100 text-dark-300'
                              : 'text-white-150 hover:bg-white-200 hover:text-dark-300',
                          )}
                        >
                          <div
                            className={cn(
                              'transition-colors',
                              selectedTab.includes(item.link)
                                ? 'text-primary-500'
                                : 'text-white-150 group-hover:text-dark-300',
                            )}
                          >
                            {item.icon}
                          </div>
                          {item.label === 'Products' ? item.label : item.label}
                        </Link>
                      ))}
                    </div>
                  </PopoverContent>
                </div>
              </Popover>
            )}
            <div
              className={cn(
                'flex flex-col transition-all duration-200 ease-in-out overflow-hidden mt-2',
                !sideBarExpanded ? 'gap-1' : 'gap-2',
                sideBarExpanded && !operationsExpanded
                  ? 'max-h-0'
                  : 'max-h-none',
              )}
            >
              {sideBarExpanded &&
                accessibleOperationsItems.map((item) => (
                  <MenuLinks
                    key={item.id}
                    {...item}
                    active={selectedTab.includes(item.link)}
                    icon={item.icon}
                    sideBarExpanded={sideBarExpanded}
                    access={item.access || false}
                  />
                ))}
            </div>
          </div>
        )}
      </div>

      {user?.company?.id && aiEnabledCompanies.includes(user?.company?.id) && (
        <ComplianceChat />
      )}
      <ComplianceChat />

      {/* ---- Footer ---- */}
      <div className={cn('mx-5 border-t border-grey-100')}>
        <Popover open={userProfile} onOpenChange={setUserProfile}>
          <PopoverTrigger asChild className="cursor-pointer">
            {sideBarExpanded ? (
              <div className="flex items-center justify-between px-2 py-2 rounded-lg group mt-2 border border-transparent hover:border-grey-200 hover:bg-white-200 transition-all duration-200">
                {/* Name and Role */}
                <div className="flex gap-2 items-center text-left leading-tight overflow-hidden">
                  <User2
                    width={20}
                    height={20}
                    className="text-white-150 group-hover:text-dark-300 transition-colors duration-200"
                  />
                  <span className="text-base text-white-150 group-hover:text-dark-300 leading-6 font-semibold transition-colors duration-200">
                    {user?.full_name || ''}
                  </span>
                </div>

                {/* Chevron dynamic */}
                {userProfile ? (
                  <ChevronUp className="h-6 w-6 shrink-0 stroke-white-150 group-hover:stroke-dark-300 transition-colors duration-200" />
                ) : (
                  <ChevronDown className="h-6 w-6 shrink-0 stroke-white-150 group-hover:stroke-dark-300 transition-colors duration-200" />
                )}
              </div>
            ) : (
              <div
                className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-sm mt-4 font-semibold text-gray-700 border border-transparent hover:border-grey-200 hover:bg-gray-300 transition-all duration-200"
                title={user?.full_name || ''}
              >
                {user?.full_name
                  ?.split(' ')
                  .map((name) => name[0])
                  .join('') || ''}
              </div>
            )}
          </PopoverTrigger>

          <MenuPopoverContent data={menuPopoverData} />
        </Popover>
      </div>
    </div>
  );
};

interface IMenuLink {
  label: string;
  icon: React.ReactNode;
  link: string;
  active?: boolean;
  sideBarExpanded: boolean;
  access: boolean;
}

const MenuLinks = ({
  label,
  icon,
  link,
  active = false,
  sideBarExpanded,
  access,
}: IMenuLink) => {
  return (
    access && (
      <Link
        className={cn(
          'flex gap-3 cursor-pointer text-base leading-6 rounded-lg font-medium p-3 group hover:bg-white-200 transition-all',
          active
            ? '!bg-primary-100 font-semibold text-dark-300'
            : 'text-white-150 hover:text-dark-300',
          !sideBarExpanded
            ? `flex-col gap-3 items-center text-xs font-semibold leading-5 p-2 ${
                active
                  ? 'text-primary-500'
                  : 'text-white-150 hover:text-dark-300'
              }`
            : 'ml-2',
        )}
        href={link}
      >
        <div className="text-white-150 group-hover:text-dark-300 transition-colors">
          {icon}
        </div>
        {label === 'Product Hub'
          ? sideBarExpanded
            ? label
            : 'Product'
          : label}
        {label !== 'Product Hub' && sideBarExpanded ? ' ' : ''}
      </Link>
    )
  );
};

export default Sidebar;
