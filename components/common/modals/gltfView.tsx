import J<PERSON>Zip from 'jszip';
import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GLTF, GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

type FileMap = Record<string, string>;
const GLTFViewer = ({ url }: { url: string }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [gltfScene, setGltfScene] = useState<THREE.Group | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene>(new THREE.Scene());
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    const width = container.clientWidth;
    const height = container.clientHeight;

    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(width, height);
    container.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    const scene = sceneRef.current;
    scene.background = null;

    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.set(0, 2, 5);
    cameraRef.current = camera;

    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(2, 2, 2);
    scene.add(directionalLight);

    controlsRef.current = new OrbitControls(camera, renderer.domElement);

    const animate = () => {
      if (controlsRef.current) controlsRef.current.update();
      renderer.render(scene, camera);
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    animate();

    const handleResize = () => {
      if (!container || !cameraRef.current || !rendererRef.current) return;
      const newWidth = container.clientWidth;
      const newHeight = container.clientHeight;
      cameraRef.current.aspect = newWidth / newHeight;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(newWidth, newHeight);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationFrameRef.current)
        cancelAnimationFrame(animationFrameRef.current);
      if (rendererRef.current) {
        rendererRef.current.dispose();
        if (rendererRef.current.domElement.parentElement) {
          rendererRef.current.domElement.parentElement.removeChild(
            rendererRef.current.domElement,
          );
        }
      }
    };
  }, []);

  useEffect(() => {
    const loadZip = async () => {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const blob = await response.blob();
        const zip = await JSZip.loadAsync(blob, {
          optimizedBinaryString: true,
        });
        let gltfContent: string | null = null;
        const files: FileMap = {};

        for (const filename of Object.keys(zip.files)) {
          const ext = filename.split('.').pop()?.toLowerCase();
          if (ext === 'gltf') {
            gltfContent = (await zip.file(filename)?.async('string')) || null;
          } else if (ext === 'bin') {
            const binBuffer = await zip.file(filename)?.async('arraybuffer');
            if (binBuffer) {
              files[filename] = URL.createObjectURL(
                new Blob([binBuffer], { type: 'application/octet-stream' }),
              );
            }
          } else if (['png', 'jpg', 'jpeg'].includes(ext || '')) {
            const blobAsset = await zip.file(filename)?.async('blob');
            if (blobAsset) {
              files[filename] = URL.createObjectURL(blobAsset);
            }
          }
        }
        if (!gltfContent) {
          console.error('No GLTF file found in ZIP.');
          return;
        }

        const loader = new GLTFLoader();
        const manager = new THREE.LoadingManager();
        manager.setURLModifier((url: string) => {
          if (files[url]) return files[url];
          const foundKey = Object.keys(files).find((key) => key.endsWith(url));
          if (foundKey) return files[foundKey];
          return url;
        });
        loader.manager = manager;
        // Parse GLTF content
        loader.parse(
          gltfContent,
          '',
          (gltf) => {
            if (gltfScene) {
              sceneRef.current.remove(gltfScene);
            }

            const box = new THREE.Box3().setFromObject(gltf.scene);
            const center = new THREE.Vector3();
            box.getCenter(center);

            gltf.scene.position.sub(center);

            sceneRef.current.add(gltf.scene);
            setGltfScene(gltf.scene);
          },
          (error) => {
            console.error('Error parsing GLTF:', error);
          },
        );
      } catch (error) {
        console.error('Error loading ZIP:', error);
      }
    };
    loadZip();
  }, [url]);
  return (
    <div style={{ width: '100%', height: '80vh' }}>
      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};
export default GLTFViewer;
