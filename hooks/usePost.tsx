import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { useState } from 'react';
import { toast } from 'react-toastify';

type UsePost = <T = unknown, B = Record<string, unknown>>() => {
  postData: (accessToken: string, path: string, body: B) => Promise<void>;
  response: T | null;
  error: AxiosError | null;
  isLoading: boolean;
};

interface ErrorResponse {
  detail?: string;
  error?: string;
}

// A generic POST hook
export const usePost: UsePost = <T, B>() => {
  const [error, setError] = useState<AxiosError | null>(null); // changed from string to AxiosError
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<T | null>(null);

  const postData = async (accessToken: string, path: string, body: B) => {
    setIsLoading(true);
    setError(null);
    setResponse(null);

    const mockUser =
      typeof window !== 'undefined'
        ? localStorage.getItem('x-mock-user')
        : null;

    const orgId =
      typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
      ...(!!mockUser ? { 'x-mock-user': mockUser } : {}),
      ...(!!orgId ? { 'x-org': orgId } : {}),
    };

    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const options: AxiosRequestConfig = {
      method: 'POST',
      url: `${baseUrl}/${productVersion}/${path}`,
      headers: headers,
      data: body, // Use "data" instead of "body" for axios
    };

    try {
      const res = await axios.request<T>(options);
      setResponse(res.data);
      console.log('--- POST Hook Response ---', res.data);
    } catch (err) {
      const axiosError = err as AxiosError;
      const errorData = axiosError.response?.data as ErrorResponse;

      if (axiosError.response) {
        const status = axiosError.response.status;

        if (status === 401) {
          window.location.reload();
        } else if (status === 404) {
          toast.error('Not found');
        } else if (status === 403) {
          const errorMessage =
            errorData?.error || errorData?.detail || 'Forbidden';
          toast.error(errorMessage);
        } else {
          const errorMessage =
            errorData?.error || errorData?.detail || axiosError.message;
          toast.error(errorMessage);
        }
        setError(axiosError);
      } else {
        // Network error or request timeout
        const errorMessage = 'Network error or request timeout';
        toast.error(errorMessage);
        setError(axiosError);
      }

      console.error('Oops! Something went wrong:', axiosError.message);
    } finally {
      setIsLoading(false);
    }
  };

  return { postData, response, isLoading, error };
};
