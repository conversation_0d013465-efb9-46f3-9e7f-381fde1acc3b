import axios from 'axios';
import { ORGANIZATION_HEADER_KEY } from '@/constants/common';

/**
 * Fetches document data by document ID
 * @param documentId - ID of the document to fetch
 * @param accessToken - Authentication token
 * @param orgId - Organization ID
 * @param router - Next.js router object for path and query extraction
 * @returns Document data from the API response
 */
export const getDocumentById = async (
  documentId: string,
  accessToken: string | null,
  orgId: string | null, // Allow null or string
  router: any,
) => {
  if (!documentId) return null;
  if (!accessToken) return null;

  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;

  const config = {
    headers: {
      Authorization: `Bearer ${accessToken}`,
      ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
    },
  };

  // Extract source from URL path
  const sourcePath = router.asPath.split('?')[0].split('/')[1] || 'standard';
  const urlId = router.query.id as string;

  // Build URL with proper fallbacks
  const url = `${baseUrl}/${productVersion}/documents/${documentId}?source=${sourcePath}&id=${
    urlId || ''
  }`;

  try {
    const response = await axios.get(url, config);
    return response.data.record;
  } catch (error) {
    console.error('Error fetching document:', error);
    return null;
  }
};
