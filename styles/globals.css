@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: Inter, Arial, Helvetica, sans-serif;
}

::-webkit-scrollbar-track {
  background-color: #f9f9f9;
  border-radius: 8px;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #f9f9f9;
}

::-webkit-scrollbar-thumb {
  background-color: #b9b9b9;
  border-radius: 8px;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --toastify-color-error: #f55d5d;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.ag-header {
  background-color: #f4f4f4 !important;
  font-family: 'Inter';
}
.ag-header .ag-header-cell-text {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  color: #989898 !important;
  font-family: 'Inter';
}
.ag-row {
  background: white !important;
}

.ag-row.ag-row-hover:before {
  background-color: #f9f9f9 !important;
}
.ag-cell {
  color: #282828;
  font-size: 1rem !important;
  font-family: 'Inter' !important;
  /* line-height: 1.5rem !important; */
  font-weight: 400 !important;
}

.mention-input-wrapper textarea:focus {
  outline: none;
}

.mention-input-wrapper textarea {
  overflow: none !important;
}

.mention-input-wrapper {
  height: 105px;
}
@layer utilities {
  .date-popper {
    @apply z-50;
  }

  .react-datepicker .react-datepicker__current-month {
    @apply text-lg font-bold bg-red py-2 w-fit rounded-[6px];
  }
}

/* calender design */
.react-datepicker-wrapper {
  width: 100%;
}
.react-datepicker__day {
  color: #282828 !important;
}

.react-datepicker__day:hover {
  background-color: #e1e1e1;
}
.react-datepicker__day--weekend {
  color: #00797d !important;
}
.react-datepicker__day--selected {
  background-color: #d2f0f0 !important;
  color: #282828 !important;
}
.react-datepicker__day--selected:hover {
  background-color: #d2f0f0 !important;
  color: #282828 !important;
}

.react-datepicker__day--keyboard-selected {
  background-color: #e5f6f6 !important;
  color: #282828 !important;
}
.react-datepicker__day--keyboard-selected:hover {
  background-color: #e5f6f6 !important;
  color: #282828 !important;
}
.react-datepicker__day-name {
  font-family: Inter;
  color: #989898 !important;
}
.react-datepicker__header {
  background-color: #fff !important;
  border-bottom: 0 !important;
}
/* .react-datepicker__input-container {
  position: absolute !important;
} */
.react-datepicker__current-month {
  background-color: #fff !important;
  text-align: center !important;
  margin-bottom: 6px !important;
  line-height: 1 !important;
  /* padding-top: 0 !important; */
  width: 100% !important;
  font-size: 0.875rem !important;
  color: #282828 !important;
  background-color: #d2f0f0 !important;
}
.react-datepicker__navigation--previous {
  top: 1.5rem !important;
  left: 6% !important;
}
.react-datepicker__navigation--next {
  top: 1.5rem !important;
  right: 6% !important;
}
.react-datepicker__navigation-icon {
  right: 0 !important;
  left: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
}
.react-datepicker__navigation-icon::before {
  border-color: #3c3c3c !important;
}
.react-datepicker__navigation--next:hover
  > .react-datepicker__navigation-icon::before {
  border-color: #282828 !important;
}

.react-datepicker__navigation--previous:hover
  > .react-datepicker__navigation-icon::before {
  border-color: #282828 !important;
}
.react-datepicker-popper[data-placement^='bottom'] .react-datepicker__triangle {
  stroke: #b9b9b9 !important;
  fill: #fff !important;
  color: #fff !important;
}

.react-datepicker__day--disabled {
  color: #989898 !important;
}

.react-datepicker__day--disabled:hover {
  color: #989898 !important;
  background-color: transparent !important;
}
.ag-root {
  min-height: 250px; /* Adjust to your needs */
}
.toast-progress.Toastify__progress-bar--error {
  background: #f55d5d !important;
}

/* .ck-editor__editable_inline {
  height: 400px;
  resize: vertical;
  overflow-y: auto;
} */

.ck-content > blockquote,
.ck-content > dl,
.ck-content > dd,
.ck-content > h1,
.ck-content > h2,
.ck-content > h3,
.ck-content > h4,
.ck-content > h5,
.ck-content > h6,
.ck-content > hr,
.ck-content > figure,
.ck-content > p,
.ck-content > pre {
  margin: revert;
}

.ck-content ol,
.ck-content ul {
  list-style: revert;
  margin: revert;
  padding: revert;
  padding-inline-start: 18px !important;
}

.ck-content > table {
  border-collapse: revert;
}

.ck-content > h1,
.ck-content > h2,
.ck-content > h3,
.ck-content > h4,
.ck-content > h5,
.ck-content > h6 {
  font-size: revert;
  font-weight: revert;
}

.main-container {
  --ckeditor5-preview-height: 700px;
  /* font-family: 'Lato'; */
  /* width: fit-content;
  margin-left: auto;
  margin-right: auto; */
}

.ck-content {
  /* font-family: 'Lato'; */
  line-height: 1.6;
  word-break: break-word;
}

.editor-container__editor-wrapper {
  display: flex;
  width: fit-content;
}

.editor-container_document-editor {
  border: 1px solid var(--ck-color-base-border);
}

.editor-container_document-editor .editor-container__toolbar {
  display: flex;
  position: relative;
  box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
}

.editor-container_document-editor .editor-container__toolbar > .ck.ck-toolbar {
  flex-grow: 1;
  width: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  border-top: 0;
  border-left: 0;
  border-right: 0;
}

.editor-container_document-editor
  .editor-container__menu-bar
  > .ck.ck-menu-bar {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  border-top: 0;
  border-left: 0;
  border-right: 0;
}

.editor-container_document-editor .editor-container__editor-wrapper {
  max-height: var(--ckeditor5-preview-height);
  min-height: var(--ckeditor5-preview-height);
  overflow-y: scroll;
  background: var(--ck-color-base-foreground);
  width: 100%;
  justify-content: center;
}

.editor-container_document-editor .editor-container__editor {
  margin-top: 28px;
  margin-bottom: 28px;
  height: 100%;
}

.editor-container_document-editor
  .editor-container__editor
  .ck.ck-editor__editable {
  box-sizing: border-box;
  min-width: calc(210mm + 2px);
  max-width: calc(210mm + 2px);
  min-height: 297mm;
  height: fit-content;
  padding: 20mm 12mm;
  border: 1px hsl(0, 0%, 82.7%) solid;
  background: hsl(0, 0%, 100%);
  box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
  flex: 1 1 auto;
  margin-left: 72px;
  margin-right: 72px;
}
